import React, { Fragment } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, Transition } from '@headlessui/react';
import { 
  UserCircleIcon, 
  Cog6ToothIcon, 
  ArrowRightOnRectangleIcon,
  CalendarDaysIcon,
  ChartBarIcon,
  UsersIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { mockUsers } from '../data/mockData';

const Navbar: React.FC = () => {
  const { user, logout, switchUser } = useAuth();
  const location = useLocation();

  const navigation = [
    { 
      name: 'Navigator Dashboard', 
      href: '/navigator', 
      icon: CalendarDaysIcon,
      roles: ['navigator'] 
    },
    { 
      name: 'Admin Dashboard', 
      href: '/admin', 
      icon: ChartBarIcon,
      roles: ['admin'] 
    },
    { 
      name: 'Navigator Directory', 
      href: '/directory', 
      icon: UsersIcon,
      roles: ['member', 'admin'] 
    },
  ];

  const filteredNavigation = navigation.filter(item => 
    user && item.roles.includes(user.role)
  );

  const handleUserSwitch = (userId: number) => {
    switchUser(userId);
  };

  return (
    <nav style={{
      backgroundColor: 'white',
      borderBottom: '1px solid #e5e7eb',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
    }}>
      <div className="max-w-7xl mx-auto px-4" style={{ maxWidth: '80rem', margin: '0 auto', padding: '0 1rem' }}>
        <div className="flex justify-between" style={{ height: '4rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div className="flex" style={{ display: 'flex', alignItems: 'center' }}>
            <div className="flex items-center" style={{ display: 'flex', alignItems: 'center' }}>
              <CalendarDaysIcon style={{ height: '2rem', width: '2rem', color: '#3b82f6' }} />
              <span style={{
                marginLeft: '0.5rem',
                fontSize: '1.25rem',
                fontWeight: 'bold',
                color: '#111827'
              }}>
                Calendly Navigator
              </span>
            </div>
            <div style={{
              display: 'flex',
              marginLeft: '1.5rem',
              gap: '2rem'
            }}>
              {filteredNavigation.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.href;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '0.5rem 1rem',
                      textDecoration: 'none',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: isActive ? '#111827' : '#6b7280',
                      backgroundColor: isActive ? '#f3f4f6' : 'transparent',
                      borderRadius: '0.375rem',
                      transition: 'all 0.2s'
                    }}
                  >
                    <Icon style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                    {item.name}
                  </Link>
                );
              })}
            </div>
          </div>

          <div className="flex items-center" style={{ display: 'flex', alignItems: 'center' }}>
            {/* Demo User Switcher */}
            <div style={{ marginRight: '1rem' }}>
              <select
                onChange={(e) => handleUserSwitch(Number(e.target.value))}
                value={user?.id || ''}
                style={{
                  fontSize: '0.875rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '0.375rem',
                  padding: '0.5rem',
                  backgroundColor: 'white'
                }}
              >
                <option value="">Switch Demo User</option>
                {mockUsers.map((demoUser) => (
                  <option key={demoUser.id} value={demoUser.id}>
                    {demoUser.name} ({demoUser.role})
                  </option>
                ))}
              </select>
            </div>

            {/* Profile dropdown */}
            <Menu as="div" style={{ position: 'relative' }}>
              <div>
                <Menu.Button style={{
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '0.875rem',
                  borderRadius: '9999px',
                  border: 'none',
                  backgroundColor: 'transparent',
                  cursor: 'pointer'
                }}>
                  <span style={{ position: 'absolute', width: '1px', height: '1px', padding: 0, margin: '-1px', overflow: 'hidden', clip: 'rect(0, 0, 0, 0)', whiteSpace: 'nowrap', border: 0 }}>Open user menu</span>
                  {user?.avatar_url ? (
                    <img
                      style={{ height: '2rem', width: '2rem', borderRadius: '50%' }}
                      src={user.avatar_url}
                      alt={user.name}
                    />
                  ) : (
                    <UserCircleIcon style={{ height: '2rem', width: '2rem', color: '#9ca3af' }} />
                  )}
                  <span style={{
                    marginLeft: '0.5rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151'
                  }}>
                    {user?.name}
                  </span>
                </Menu.Button>
              </div>
              <Transition
                as={Fragment}
                enter="transition ease-out duration-200"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items style={{
                  position: 'absolute',
                  right: 0,
                  marginTop: '0.5rem',
                  width: '12rem',
                  borderRadius: '0.375rem',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                  padding: '0.25rem 0',
                  backgroundColor: 'white',
                  border: '1px solid rgba(0, 0, 0, 0.05)',
                  zIndex: 50
                }}>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        style={{
                          backgroundColor: active ? '#f3f4f6' : 'transparent',
                          display: 'flex',
                          alignItems: 'center',
                          padding: '0.5rem 1rem',
                          fontSize: '0.875rem',
                          color: '#374151',
                          width: '100%',
                          textAlign: 'left',
                          border: 'none',
                          cursor: 'pointer'
                        }}
                      >
                        <Cog6ToothIcon style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                        Settings
                      </button>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={logout}
                        style={{
                          backgroundColor: active ? '#f3f4f6' : 'transparent',
                          display: 'flex',
                          alignItems: 'center',
                          padding: '0.5rem 1rem',
                          fontSize: '0.875rem',
                          color: '#374151',
                          width: '100%',
                          textAlign: 'left',
                          border: 'none',
                          cursor: 'pointer'
                        }}
                      >
                        <ArrowRightOnRectangleIcon style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                        Sign out
                      </button>
                    )}
                  </Menu.Item>
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
