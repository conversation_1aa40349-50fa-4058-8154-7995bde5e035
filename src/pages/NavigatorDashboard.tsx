import React, { useState } from 'react';
import { 
  CalendarDaysIcon, 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  LinkIcon,
  EyeIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { mockNavigators, mockEventTypes } from '../data/mockData';
import CalendlyConnectionCard from '../components/CalendlyConnectionCard';
import EventTypesManager from '../components/EventTypesManager';
import BookingPreview from '../components/BookingPreview';

const NavigatorDashboard: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'overview' | 'events' | 'preview'>('overview');
  
  // Find the navigator data for the current user
  const navigator = mockNavigators.find(nav => nav.email === user?.email);
  
  if (!navigator) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Navigator Not Found</h1>
          <p className="text-gray-600">Unable to find navigator profile for this user.</p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: CalendarDaysIcon },
    { id: 'events', name: 'Event Types', icon: Cog6ToothIcon },
    { id: 'preview', name: 'Preview', icon: EyeIcon },
  ];

  return (
    <div className="page-container">
      {/* Header */}
      <div className="page-header">
        <h1 className="page-title">Navigator Dashboard</h1>
        <p className="page-subtitle">
          Manage your Calendly integration and booking settings
        </p>
      </div>

      {/* Status Banner */}
      <div style={{
        marginBottom: '2rem',
        borderRadius: '0.5rem',
        padding: '1rem',
        backgroundColor: navigator.calendly_connected ? '#f0fdf4' : '#fffbeb',
        border: `1px solid ${navigator.calendly_connected ? '#bbf7d0' : '#fed7aa'}`
      }}>
        <div className="flex items-center" style={{ display: 'flex', alignItems: 'center' }}>
          {navigator.calendly_connected ? (
            <CheckCircleIcon style={{ height: '1.25rem', width: '1.25rem', color: '#059669', marginRight: '0.5rem' }} />
          ) : (
            <ExclamationTriangleIcon style={{ height: '1.25rem', width: '1.25rem', color: '#d97706', marginRight: '0.5rem' }} />
          )}
          <span style={{
            fontWeight: '500',
            color: navigator.calendly_connected ? '#065f46' : '#92400e'
          }}>
            {navigator.calendly_connected
              ? 'Calendly Connected Successfully'
              : 'Calendly Not Connected'
            }
          </span>
        </div>
        {navigator.calendly_connected && navigator.calendly_profile && (
          <p style={{
            marginTop: '0.25rem',
            fontSize: '0.875rem',
            color: '#047857'
          }}>
            Connected as {navigator.calendly_profile.name} ({navigator.calendly_profile.email})
          </p>
        )}
      </div>

      {/* Tabs */}
      <div style={{ borderBottom: '1px solid #e5e7eb', marginBottom: '2rem' }}>
        <nav style={{ display: 'flex', gap: '2rem', marginBottom: '-1px' }}>
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                style={{
                  whiteSpace: 'nowrap',
                  padding: '0.5rem 0.25rem',
                  borderBottom: `2px solid ${isActive ? '#3b82f6' : 'transparent'}`,
                  fontWeight: '500',
                  fontSize: '0.875rem',
                  display: 'flex',
                  alignItems: 'center',
                  color: isActive ? '#3b82f6' : '#6b7280',
                  backgroundColor: 'transparent',
                  border: 'none',
                  cursor: 'pointer',
                  transition: 'color 0.2s'
                }}
                onMouseOver={(e) => !isActive && (e.currentTarget.style.color = '#374151')}
                onMouseOut={(e) => !isActive && (e.currentTarget.style.color = '#6b7280')}
              >
                <Icon style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'overview' && (
          <div className="grid lg:grid-cols-2" style={{ gap: '1.5rem' }}>
            <CalendlyConnectionCard navigator={navigator} />

            {/* Quick Stats */}
            <div className="card">
              <h3 style={{ fontSize: '1.125rem', fontWeight: '500', color: '#111827', marginBottom: '1rem' }}>
                Quick Stats
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                <div className="flex justify-between">
                  <span style={{ color: '#6b7280' }}>Total Bookings</span>
                  <span style={{ fontWeight: '600', color: '#111827' }}>{navigator.total_bookings}</span>
                </div>
                <div className="flex justify-between">
                  <span style={{ color: '#6b7280' }}>Specialty</span>
                  <span style={{ fontWeight: '600', color: '#111827' }}>{navigator.specialty}</span>
                </div>
                <div className="flex justify-between">
                  <span style={{ color: '#6b7280' }}>Member Since</span>
                  <span style={{ fontWeight: '600', color: '#111827' }}>
                    {new Date(navigator.created_at).toLocaleDateString()}
                  </span>
                </div>
                {navigator.calendly_connected && navigator.calendly_profile && (
                  <div style={{ paddingTop: '1rem', borderTop: '1px solid #e5e7eb' }}>
                    <a
                      href={navigator.calendly_profile.scheduling_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        color: '#3b82f6',
                        textDecoration: 'none'
                      }}
                    >
                      <LinkIcon style={{ height: '1rem', width: '1rem', marginRight: '0.25rem' }} />
                      View Public Calendly Page
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'events' && (
          <EventTypesManager 
            navigator={navigator} 
            eventTypes={mockEventTypes}
          />
        )}

        {activeTab === 'preview' && (
          <BookingPreview navigator={navigator} />
        )}
      </div>
    </div>
  );
};

export default NavigatorDashboard;
