{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/CalendlyConnectionCard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { CalendarDaysIcon, CheckCircleIcon, XCircleIcon, ArrowPathIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CalendlyConnectionCard = ({\n  navigator\n}) => {\n  _s();\n  const [isConnecting, setIsConnecting] = useState(false);\n  const [isDisconnecting, setIsDisconnecting] = useState(false);\n  const [showDisconnectConfirm, setShowDisconnectConfirm] = useState(false);\n  const handleConnect = async () => {\n    setIsConnecting(true);\n\n    // Simulate OAuth flow\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    // In a real app, this would redirect to Calendly OAuth\n    alert('Demo: This would redirect to Calendly OAuth flow. For demo purposes, we\\'ll simulate a successful connection.');\n    setIsConnecting(false);\n\n    // Simulate successful connection by reloading (in real app, this would update state)\n    window.location.reload();\n  };\n  const handleDisconnect = async () => {\n    setIsDisconnecting(true);\n\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    alert('Demo: Calendly account would be disconnected. Page will reload to show disconnected state.');\n    setIsDisconnecting(false);\n    setShowDisconnectConfirm(false);\n\n    // In real app, this would update the navigator state\n    window.location.reload();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center\",\n      style: {\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CalendarDaysIcon, {\n        style: {\n          height: '1.5rem',\n          width: '1.5rem',\n          color: '#3b82f6',\n          marginRight: '0.5rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          fontSize: '1.125rem',\n          fontWeight: '500',\n          color: '#111827'\n        },\n        children: \"Calendly Integration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), navigator.calendly_connected ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        style: {\n          color: '#059669'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          style: {\n            height: '1.25rem',\n            width: '1.25rem',\n            marginRight: '0.5rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '500'\n          },\n          children: \"Connected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this), navigator.calendly_profile && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f9fafb',\n          borderRadius: '0.5rem',\n          padding: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          style: {\n            marginBottom: '0.75rem'\n          },\n          children: [navigator.calendly_profile.avatar_url && /*#__PURE__*/_jsxDEV(\"img\", {\n            src: navigator.calendly_profile.avatar_url,\n            alt: navigator.calendly_profile.name,\n            style: {\n              height: '2.5rem',\n              width: '2.5rem',\n              borderRadius: '50%',\n              marginRight: '0.75rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontWeight: '500',\n                color: '#111827'\n              },\n              children: navigator.calendly_profile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#6b7280'\n              },\n              children: navigator.calendly_profile.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#6b7280'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Timezone:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 20\n            }, this), \" \", navigator.calendly_profile.timezone]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Scheduling URL:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 20\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: navigator.calendly_profile.scheduling_url,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              style: {\n                color: '#3b82f6',\n                textDecoration: 'none',\n                marginLeft: '0.25rem'\n              },\n              children: navigator.calendly_profile.scheduling_url\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 13\n      }, this), !showDisconnectConfirm ? /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowDisconnectConfirm(true),\n        style: {\n          color: '#dc2626',\n          fontSize: '0.875rem',\n          fontWeight: '500',\n          backgroundColor: 'transparent',\n          border: 'none',\n          cursor: 'pointer'\n        },\n        children: \"Disconnect Calendly\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '0.5rem',\n          padding: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          style: {\n            marginBottom: '0.75rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n            style: {\n              height: '1.25rem',\n              width: '1.25rem',\n              color: '#dc2626',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: '500',\n              color: '#991b1b'\n            },\n            children: \"Confirm Disconnection\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#b91c1c',\n            marginBottom: '1rem'\n          },\n          children: \"Are you sure you want to disconnect your Calendly account? Members will no longer be able to book appointments with you.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex\",\n          style: {\n            gap: '0.75rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleDisconnect,\n            disabled: isDisconnecting,\n            style: {\n              backgroundColor: '#dc2626',\n              color: 'white',\n              padding: '0.5rem 1rem',\n              borderRadius: '0.375rem',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              border: 'none',\n              cursor: 'pointer',\n              opacity: isDisconnecting ? 0.5 : 1\n            },\n            children: isDisconnecting ? 'Disconnecting...' : 'Yes, Disconnect'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowDisconnectConfirm(false),\n            style: {\n              backgroundColor: 'white',\n              color: '#374151',\n              padding: '0.5rem 1rem',\n              borderRadius: '0.375rem',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              border: '1px solid #d1d5db',\n              cursor: 'pointer'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        style: {\n          color: '#6b7280'\n        },\n        children: [/*#__PURE__*/_jsxDEV(XCircleIcon, {\n          style: {\n            height: '1.25rem',\n            width: '1.25rem',\n            marginRight: '0.5rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '500'\n          },\n          children: \"Not Connected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#6b7280',\n          fontSize: '0.875rem'\n        },\n        children: \"Connect your Calendly account to allow members to book appointments with you directly through our platform.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#eff6ff',\n          border: '1px solid #bfdbfe',\n          borderRadius: '0.5rem',\n          padding: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            fontWeight: '500',\n            color: '#1e3a8a',\n            marginBottom: '0.5rem'\n          },\n          children: \"What happens when you connect?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            fontSize: '0.875rem',\n            color: '#1e40af',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.25rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Members can book appointments without leaving our platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Your existing Calendly settings and availability remain unchanged\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 You'll receive notifications for all bookings as usual\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 You can disconnect at any time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleConnect,\n        disabled: isConnecting,\n        className: \"btn btn-primary\",\n        style: {\n          width: '100%',\n          backgroundColor: '#3b82f6',\n          color: 'white',\n          padding: '0.75rem 1rem',\n          borderRadius: '0.375rem',\n          fontWeight: '500',\n          border: 'none',\n          cursor: 'pointer',\n          opacity: isConnecting ? 0.5 : 1,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: isConnecting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(ArrowPathIcon, {\n            style: {\n              height: '1rem',\n              width: '1rem',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 17\n          }, this), \"Connecting to Calendly...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(CalendarDaysIcon, {\n            style: {\n              height: '1rem',\n              width: '1rem',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 17\n          }, this), \"Connect Calendly Account\"]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(CalendlyConnectionCard, \"wI8j5YU4Ggo0mqpRRSUsbYo5DVY=\");\n_c = CalendlyConnectionCard;\nexport default CalendlyConnectionCard;\nvar _c;\n$RefreshReg$(_c, \"CalendlyConnectionCard\");", "map": {"version": 3, "names": ["React", "useState", "CalendarDaysIcon", "CheckCircleIcon", "XCircleIcon", "ArrowPathIcon", "ExclamationTriangleIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CalendlyConnectionCard", "navigator", "_s", "isConnecting", "setIsConnecting", "isDisconnecting", "setIsDisconnecting", "showDisconnectConfirm", "setShowDisconnectConfirm", "handleConnect", "Promise", "resolve", "setTimeout", "alert", "window", "location", "reload", "handleDisconnect", "className", "children", "style", "marginBottom", "height", "width", "color", "marginRight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "fontWeight", "calendly_connected", "display", "flexDirection", "gap", "calendly_profile", "backgroundColor", "borderRadius", "padding", "avatar_url", "src", "alt", "name", "email", "timezone", "href", "scheduling_url", "target", "rel", "textDecoration", "marginLeft", "onClick", "border", "cursor", "disabled", "opacity", "alignItems", "justifyContent", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/CalendlyConnectionCard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { \n  CalendarDaysIcon, \n  CheckCircleIcon, \n  XCircleIcon,\n  ArrowPathIcon,\n  ExclamationTriangleIcon\n} from '@heroicons/react/24/outline';\nimport { Navigator } from '../types';\n\ninterface CalendlyConnectionCardProps {\n  navigator: Navigator;\n}\n\nconst CalendlyConnectionCard: React.FC<CalendlyConnectionCardProps> = ({ navigator }) => {\n  const [isConnecting, setIsConnecting] = useState(false);\n  const [isDisconnecting, setIsDisconnecting] = useState(false);\n  const [showDisconnectConfirm, setShowDisconnectConfirm] = useState(false);\n\n  const handleConnect = async () => {\n    setIsConnecting(true);\n    \n    // Simulate OAuth flow\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    // In a real app, this would redirect to Calendly OAuth\n    alert('Demo: This would redirect to Calendly OAuth flow. For demo purposes, we\\'ll simulate a successful connection.');\n    \n    setIsConnecting(false);\n    \n    // Simulate successful connection by reloading (in real app, this would update state)\n    window.location.reload();\n  };\n\n  const handleDisconnect = async () => {\n    setIsDisconnecting(true);\n    \n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    alert('Demo: Calendly account would be disconnected. Page will reload to show disconnected state.');\n    \n    setIsDisconnecting(false);\n    setShowDisconnectConfirm(false);\n    \n    // In real app, this would update the navigator state\n    window.location.reload();\n  };\n\n  return (\n    <div className=\"card\">\n      <div className=\"flex items-center\" style={{ marginBottom: '1rem' }}>\n        <CalendarDaysIcon style={{ height: '1.5rem', width: '1.5rem', color: '#3b82f6', marginRight: '0.5rem' }} />\n        <h3 style={{ fontSize: '1.125rem', fontWeight: '500', color: '#111827' }}>Calendly Integration</h3>\n      </div>\n\n      {navigator.calendly_connected ? (\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          {/* Connected State */}\n          <div className=\"flex items-center\" style={{ color: '#059669' }}>\n            <CheckCircleIcon style={{ height: '1.25rem', width: '1.25rem', marginRight: '0.5rem' }} />\n            <span style={{ fontWeight: '500' }}>Connected</span>\n          </div>\n\n          {navigator.calendly_profile && (\n            <div style={{\n              backgroundColor: '#f9fafb',\n              borderRadius: '0.5rem',\n              padding: '1rem'\n            }}>\n              <div className=\"flex items-center\" style={{ marginBottom: '0.75rem' }}>\n                {navigator.calendly_profile.avatar_url && (\n                  <img\n                    src={navigator.calendly_profile.avatar_url}\n                    alt={navigator.calendly_profile.name}\n                    style={{\n                      height: '2.5rem',\n                      width: '2.5rem',\n                      borderRadius: '50%',\n                      marginRight: '0.75rem'\n                    }}\n                  />\n                )}\n                <div>\n                  <p style={{ fontWeight: '500', color: '#111827' }}>{navigator.calendly_profile.name}</p>\n                  <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>{navigator.calendly_profile.email}</p>\n                </div>\n              </div>\n              <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n                <p><strong>Timezone:</strong> {navigator.calendly_profile.timezone}</p>\n                <p><strong>Scheduling URL:</strong>\n                  <a\n                    href={navigator.calendly_profile.scheduling_url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    style={{\n                      color: '#3b82f6',\n                      textDecoration: 'none',\n                      marginLeft: '0.25rem'\n                    }}\n                  >\n                    {navigator.calendly_profile.scheduling_url}\n                  </a>\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Disconnect Section */}\n          {!showDisconnectConfirm ? (\n            <button\n              onClick={() => setShowDisconnectConfirm(true)}\n              style={{\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                backgroundColor: 'transparent',\n                border: 'none',\n                cursor: 'pointer'\n              }}\n            >\n              Disconnect Calendly\n            </button>\n          ) : (\n            <div style={{\n              backgroundColor: '#fef2f2',\n              border: '1px solid #fecaca',\n              borderRadius: '0.5rem',\n              padding: '1rem'\n            }}>\n              <div className=\"flex items-center\" style={{ marginBottom: '0.75rem' }}>\n                <ExclamationTriangleIcon style={{ height: '1.25rem', width: '1.25rem', color: '#dc2626', marginRight: '0.5rem' }} />\n                <span style={{ fontWeight: '500', color: '#991b1b' }}>Confirm Disconnection</span>\n              </div>\n              <p style={{ fontSize: '0.875rem', color: '#b91c1c', marginBottom: '1rem' }}>\n                Are you sure you want to disconnect your Calendly account? Members will no longer be able to book appointments with you.\n              </p>\n              <div className=\"flex\" style={{ gap: '0.75rem' }}>\n                <button\n                  onClick={handleDisconnect}\n                  disabled={isDisconnecting}\n                  style={{\n                    backgroundColor: '#dc2626',\n                    color: 'white',\n                    padding: '0.5rem 1rem',\n                    borderRadius: '0.375rem',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    border: 'none',\n                    cursor: 'pointer',\n                    opacity: isDisconnecting ? 0.5 : 1\n                  }}\n                >\n                  {isDisconnecting ? 'Disconnecting...' : 'Yes, Disconnect'}\n                </button>\n                <button\n                  onClick={() => setShowDisconnectConfirm(false)}\n                  style={{\n                    backgroundColor: 'white',\n                    color: '#374151',\n                    padding: '0.5rem 1rem',\n                    borderRadius: '0.375rem',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    border: '1px solid #d1d5db',\n                    cursor: 'pointer'\n                  }}\n                >\n                  Cancel\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      ) : (\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          {/* Disconnected State */}\n          <div className=\"flex items-center\" style={{ color: '#6b7280' }}>\n            <XCircleIcon style={{ height: '1.25rem', width: '1.25rem', marginRight: '0.5rem' }} />\n            <span style={{ fontWeight: '500' }}>Not Connected</span>\n          </div>\n\n          <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n            Connect your Calendly account to allow members to book appointments with you directly through our platform.\n          </p>\n\n          <div style={{\n            backgroundColor: '#eff6ff',\n            border: '1px solid #bfdbfe',\n            borderRadius: '0.5rem',\n            padding: '1rem'\n          }}>\n            <h4 style={{ fontWeight: '500', color: '#1e3a8a', marginBottom: '0.5rem' }}>What happens when you connect?</h4>\n            <ul style={{ fontSize: '0.875rem', color: '#1e40af', display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>\n              <li>• Members can book appointments without leaving our platform</li>\n              <li>• Your existing Calendly settings and availability remain unchanged</li>\n              <li>• You'll receive notifications for all bookings as usual</li>\n              <li>• You can disconnect at any time</li>\n            </ul>\n          </div>\n\n          <button\n            onClick={handleConnect}\n            disabled={isConnecting}\n            className=\"btn btn-primary\"\n            style={{\n              width: '100%',\n              backgroundColor: '#3b82f6',\n              color: 'white',\n              padding: '0.75rem 1rem',\n              borderRadius: '0.375rem',\n              fontWeight: '500',\n              border: 'none',\n              cursor: 'pointer',\n              opacity: isConnecting ? 0.5 : 1,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}\n          >\n            {isConnecting ? (\n              <>\n                <ArrowPathIcon style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />\n                Connecting to Calendly...\n              </>\n            ) : (\n              <>\n                <CalendarDaysIcon style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />\n                Connect Calendly Account\n              </>\n            )}\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CalendlyConnectionCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,gBAAgB,EAChBC,eAAe,EACfC,WAAW,EACXC,aAAa,EACbC,uBAAuB,QAClB,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOrC,MAAMC,sBAA6D,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACvF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEzE,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCL,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,MAAM,IAAIM,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACAE,KAAK,CAAC,+GAA+G,CAAC;IAEtHT,eAAe,CAAC,KAAK,CAAC;;IAEtB;IACAU,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCX,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAM,IAAII,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvDE,KAAK,CAAC,4FAA4F,CAAC;IAEnGP,kBAAkB,CAAC,KAAK,CAAC;IACzBE,wBAAwB,CAAC,KAAK,CAAC;;IAE/B;IACAM,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,oBACEnB,OAAA;IAAKqB,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBtB,OAAA;MAAKqB,SAAS,EAAC,mBAAmB;MAACE,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACjEtB,OAAA,CAACN,gBAAgB;QAAC6B,KAAK,EAAE;UAAEE,MAAM,EAAE,QAAQ;UAAEC,KAAK,EAAE,QAAQ;UAAEC,KAAK,EAAE,SAAS;UAAEC,WAAW,EAAE;QAAS;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3GhC,OAAA;QAAIuB,KAAK,EAAE;UAAEU,QAAQ,EAAE,UAAU;UAAEC,UAAU,EAAE,KAAK;UAAEP,KAAK,EAAE;QAAU,CAAE;QAAAL,QAAA,EAAC;MAAoB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChG,CAAC,EAEL5B,SAAS,CAAC+B,kBAAkB,gBAC3BnC,OAAA;MAAKuB,KAAK,EAAE;QAAEa,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAhB,QAAA,gBAEpEtB,OAAA;QAAKqB,SAAS,EAAC,mBAAmB;QAACE,KAAK,EAAE;UAAEI,KAAK,EAAE;QAAU,CAAE;QAAAL,QAAA,gBAC7DtB,OAAA,CAACL,eAAe;UAAC4B,KAAK,EAAE;YAAEE,MAAM,EAAE,SAAS;YAAEC,KAAK,EAAE,SAAS;YAAEE,WAAW,EAAE;UAAS;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1FhC,OAAA;UAAMuB,KAAK,EAAE;YAAEW,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAC;QAAS;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,EAEL5B,SAAS,CAACmC,gBAAgB,iBACzBvC,OAAA;QAAKuB,KAAK,EAAE;UACViB,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,QAAQ;UACtBC,OAAO,EAAE;QACX,CAAE;QAAApB,QAAA,gBACAtB,OAAA;UAAKqB,SAAS,EAAC,mBAAmB;UAACE,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAU,CAAE;UAAAF,QAAA,GACnElB,SAAS,CAACmC,gBAAgB,CAACI,UAAU,iBACpC3C,OAAA;YACE4C,GAAG,EAAExC,SAAS,CAACmC,gBAAgB,CAACI,UAAW;YAC3CE,GAAG,EAAEzC,SAAS,CAACmC,gBAAgB,CAACO,IAAK;YACrCvB,KAAK,EAAE;cACLE,MAAM,EAAE,QAAQ;cAChBC,KAAK,EAAE,QAAQ;cACfe,YAAY,EAAE,KAAK;cACnBb,WAAW,EAAE;YACf;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF,eACDhC,OAAA;YAAAsB,QAAA,gBACEtB,OAAA;cAAGuB,KAAK,EAAE;gBAAEW,UAAU,EAAE,KAAK;gBAAEP,KAAK,EAAE;cAAU,CAAE;cAAAL,QAAA,EAAElB,SAAS,CAACmC,gBAAgB,CAACO;YAAI;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxFhC,OAAA;cAAGuB,KAAK,EAAE;gBAAEU,QAAQ,EAAE,UAAU;gBAAEN,KAAK,EAAE;cAAU,CAAE;cAAAL,QAAA,EAAElB,SAAS,CAACmC,gBAAgB,CAACQ;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhC,OAAA;UAAKuB,KAAK,EAAE;YAAEU,QAAQ,EAAE,UAAU;YAAEN,KAAK,EAAE;UAAU,CAAE;UAAAL,QAAA,gBACrDtB,OAAA;YAAAsB,QAAA,gBAAGtB,OAAA;cAAAsB,QAAA,EAAQ;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5B,SAAS,CAACmC,gBAAgB,CAACS,QAAQ;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEhC,OAAA;YAAAsB,QAAA,gBAAGtB,OAAA;cAAAsB,QAAA,EAAQ;YAAe;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjChC,OAAA;cACEiD,IAAI,EAAE7C,SAAS,CAACmC,gBAAgB,CAACW,cAAe;cAChDC,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzB7B,KAAK,EAAE;gBACLI,KAAK,EAAE,SAAS;gBAChB0B,cAAc,EAAE,MAAM;gBACtBC,UAAU,EAAE;cACd,CAAE;cAAAhC,QAAA,EAEDlB,SAAS,CAACmC,gBAAgB,CAACW;YAAc;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACtB,qBAAqB,gBACrBV,OAAA;QACEuD,OAAO,EAAEA,CAAA,KAAM5C,wBAAwB,CAAC,IAAI,CAAE;QAC9CY,KAAK,EAAE;UACLI,KAAK,EAAE,SAAS;UAChBM,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBM,eAAe,EAAE,aAAa;UAC9BgB,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE;QACV,CAAE;QAAAnC,QAAA,EACH;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAEThC,OAAA;QAAKuB,KAAK,EAAE;UACViB,eAAe,EAAE,SAAS;UAC1BgB,MAAM,EAAE,mBAAmB;UAC3Bf,YAAY,EAAE,QAAQ;UACtBC,OAAO,EAAE;QACX,CAAE;QAAApB,QAAA,gBACAtB,OAAA;UAAKqB,SAAS,EAAC,mBAAmB;UAACE,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAU,CAAE;UAAAF,QAAA,gBACpEtB,OAAA,CAACF,uBAAuB;YAACyB,KAAK,EAAE;cAAEE,MAAM,EAAE,SAAS;cAAEC,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE,SAAS;cAAEC,WAAW,EAAE;YAAS;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpHhC,OAAA;YAAMuB,KAAK,EAAE;cAAEW,UAAU,EAAE,KAAK;cAAEP,KAAK,EAAE;YAAU,CAAE;YAAAL,QAAA,EAAC;UAAqB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eACNhC,OAAA;UAAGuB,KAAK,EAAE;YAAEU,QAAQ,EAAE,UAAU;YAAEN,KAAK,EAAE,SAAS;YAAEH,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAE5E;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJhC,OAAA;UAAKqB,SAAS,EAAC,MAAM;UAACE,KAAK,EAAE;YAAEe,GAAG,EAAE;UAAU,CAAE;UAAAhB,QAAA,gBAC9CtB,OAAA;YACEuD,OAAO,EAAEnC,gBAAiB;YAC1BsC,QAAQ,EAAElD,eAAgB;YAC1Be,KAAK,EAAE;cACLiB,eAAe,EAAE,SAAS;cAC1Bb,KAAK,EAAE,OAAO;cACde,OAAO,EAAE,aAAa;cACtBD,YAAY,EAAE,UAAU;cACxBR,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBsB,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBE,OAAO,EAAEnD,eAAe,GAAG,GAAG,GAAG;YACnC,CAAE;YAAAc,QAAA,EAEDd,eAAe,GAAG,kBAAkB,GAAG;UAAiB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACThC,OAAA;YACEuD,OAAO,EAAEA,CAAA,KAAM5C,wBAAwB,CAAC,KAAK,CAAE;YAC/CY,KAAK,EAAE;cACLiB,eAAe,EAAE,OAAO;cACxBb,KAAK,EAAE,SAAS;cAChBe,OAAO,EAAE,aAAa;cACtBD,YAAY,EAAE,UAAU;cACxBR,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBsB,MAAM,EAAE,mBAAmB;cAC3BC,MAAM,EAAE;YACV,CAAE;YAAAnC,QAAA,EACH;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,gBAENhC,OAAA;MAAKuB,KAAK,EAAE;QAAEa,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAhB,QAAA,gBAEpEtB,OAAA;QAAKqB,SAAS,EAAC,mBAAmB;QAACE,KAAK,EAAE;UAAEI,KAAK,EAAE;QAAU,CAAE;QAAAL,QAAA,gBAC7DtB,OAAA,CAACJ,WAAW;UAAC2B,KAAK,EAAE;YAAEE,MAAM,EAAE,SAAS;YAAEC,KAAK,EAAE,SAAS;YAAEE,WAAW,EAAE;UAAS;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtFhC,OAAA;UAAMuB,KAAK,EAAE;YAAEW,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAC;QAAa;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eAENhC,OAAA;QAAGuB,KAAK,EAAE;UAAEI,KAAK,EAAE,SAAS;UAAEM,QAAQ,EAAE;QAAW,CAAE;QAAAX,QAAA,EAAC;MAEtD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJhC,OAAA;QAAKuB,KAAK,EAAE;UACViB,eAAe,EAAE,SAAS;UAC1BgB,MAAM,EAAE,mBAAmB;UAC3Bf,YAAY,EAAE,QAAQ;UACtBC,OAAO,EAAE;QACX,CAAE;QAAApB,QAAA,gBACAtB,OAAA;UAAIuB,KAAK,EAAE;YAAEW,UAAU,EAAE,KAAK;YAAEP,KAAK,EAAE,SAAS;YAAEH,YAAY,EAAE;UAAS,CAAE;UAAAF,QAAA,EAAC;QAA8B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/GhC,OAAA;UAAIuB,KAAK,EAAE;YAAEU,QAAQ,EAAE,UAAU;YAAEN,KAAK,EAAE,SAAS;YAAES,OAAO,EAAE,MAAM;YAAEC,aAAa,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAU,CAAE;UAAAhB,QAAA,gBAC9GtB,OAAA;YAAAsB,QAAA,EAAI;UAA4D;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrEhC,OAAA;YAAAsB,QAAA,EAAI;UAAmE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5EhC,OAAA;YAAAsB,QAAA,EAAI;UAAwD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjEhC,OAAA;YAAAsB,QAAA,EAAI;UAAgC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENhC,OAAA;QACEuD,OAAO,EAAE3C,aAAc;QACvB8C,QAAQ,EAAEpD,YAAa;QACvBe,SAAS,EAAC,iBAAiB;QAC3BE,KAAK,EAAE;UACLG,KAAK,EAAE,MAAM;UACbc,eAAe,EAAE,SAAS;UAC1Bb,KAAK,EAAE,OAAO;UACde,OAAO,EAAE,cAAc;UACvBD,YAAY,EAAE,UAAU;UACxBP,UAAU,EAAE,KAAK;UACjBsB,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,SAAS;UACjBE,OAAO,EAAErD,YAAY,GAAG,GAAG,GAAG,CAAC;UAC/B8B,OAAO,EAAE,MAAM;UACfwB,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAvC,QAAA,EAEDhB,YAAY,gBACXN,OAAA,CAAAE,SAAA;UAAAoB,QAAA,gBACEtB,OAAA,CAACH,aAAa;YAAC0B,KAAK,EAAE;cAAEE,MAAM,EAAE,MAAM;cAAEC,KAAK,EAAE,MAAM;cAAEE,WAAW,EAAE;YAAS;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAEpF;QAAA,eAAE,CAAC,gBAEHhC,OAAA,CAAAE,SAAA;UAAAoB,QAAA,gBACEtB,OAAA,CAACN,gBAAgB;YAAC6B,KAAK,EAAE;cAAEE,MAAM,EAAE,MAAM;cAAEC,KAAK,EAAE,MAAM;cAAEE,WAAW,EAAE;YAAS;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAEvF;QAAA,eAAE;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA9NIF,sBAA6D;AAAA2D,EAAA,GAA7D3D,sBAA6D;AAgOnE,eAAeA,sBAAsB;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}