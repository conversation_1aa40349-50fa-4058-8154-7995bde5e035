[{"/Users/<USER>/Documents/augment-projects/calendly-navigator/src/index.tsx": "1", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/App.tsx": "3", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/pages/NavigatorDashboard.tsx": "4", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/pages/LoginPage.tsx": "5", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/LoadingSpinner.tsx": "6", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/contexts/AuthContext.tsx": "7", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/Layout.tsx": "8", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/CalendlyConnectionCard.tsx": "9", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/EventTypesManager.tsx": "10", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/BookingPreview.tsx": "11", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/Navbar.tsx": "12", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/data/mockData.ts": "13", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/pages/MemberDirectory.tsx": "14", "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/pages/AdminDashboard.tsx": "15"}, {"size": 554, "mtime": 1755362658653, "results": "16", "hashOfConfig": "17"}, {"size": 425, "mtime": 1755362658653, "results": "18", "hashOfConfig": "17"}, {"size": 3299, "mtime": 1755362819693, "results": "19", "hashOfConfig": "17"}, {"size": 7065, "mtime": 1755368608971, "results": "20", "hashOfConfig": "17"}, {"size": 6656, "mtime": 1755368402455, "results": "21", "hashOfConfig": "17"}, {"size": 412, "mtime": 1755363591453, "results": "22", "hashOfConfig": "17"}, {"size": 2898, "mtime": 1755363164234, "results": "23", "hashOfConfig": "17"}, {"size": 298, "mtime": 1755362837785, "results": "24", "hashOfConfig": "17"}, {"size": 9223, "mtime": 1755368702375, "results": "25", "hashOfConfig": "17"}, {"size": 6207, "mtime": 1755363001226, "results": "26", "hashOfConfig": "17"}, {"size": 6538, "mtime": 1755363038614, "results": "27", "hashOfConfig": "17"}, {"size": 8532, "mtime": 1755368495828, "results": "28", "hashOfConfig": "17"}, {"size": 6046, "mtime": 1755362778921, "results": "29", "hashOfConfig": "17"}, {"size": 7730, "mtime": 1755363155587, "results": "30", "hashOfConfig": "17"}, {"size": 8278, "mtime": 1755363215828, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12h8lzk", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/index.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/App.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/pages/NavigatorDashboard.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/pages/LoginPage.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/Layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/CalendlyConnectionCard.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/EventTypesManager.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/BookingPreview.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/components/Navbar.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/data/mockData.ts", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/pages/MemberDirectory.tsx", [], [], "/Users/<USER>/Documents/augment-projects/calendly-navigator/src/pages/AdminDashboard.tsx", [], []]