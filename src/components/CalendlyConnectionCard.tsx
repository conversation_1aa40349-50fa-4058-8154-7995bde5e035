import React, { useState } from 'react';
import { 
  CalendarDaysIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { Navigator } from '../types';

interface CalendlyConnectionCardProps {
  navigator: Navigator;
}

const CalendlyConnectionCard: React.FC<CalendlyConnectionCardProps> = ({ navigator }) => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [showDisconnectConfirm, setShowDisconnectConfirm] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);
    
    // Simulate OAuth flow
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // In a real app, this would redirect to Calendly OAuth
    alert('Demo: This would redirect to Calendly OAuth flow. For demo purposes, we\'ll simulate a successful connection.');
    
    setIsConnecting(false);
    
    // Simulate successful connection by reloading (in real app, this would update state)
    window.location.reload();
  };

  const handleDisconnect = async () => {
    setIsDisconnecting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    alert('Demo: Calendly account would be disconnected. Page will reload to show disconnected state.');
    
    setIsDisconnecting(false);
    setShowDisconnectConfirm(false);
    
    // In real app, this would update the navigator state
    window.location.reload();
  };

  return (
    <div className="card">
      <div className="flex items-center" style={{ marginBottom: '1rem' }}>
        <CalendarDaysIcon style={{ height: '1.5rem', width: '1.5rem', color: '#3b82f6', marginRight: '0.5rem' }} />
        <h3 style={{ fontSize: '1.125rem', fontWeight: '500', color: '#111827' }}>Calendly Integration</h3>
      </div>

      {navigator.calendly_connected ? (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          {/* Connected State */}
          <div className="flex items-center" style={{ color: '#059669' }}>
            <CheckCircleIcon style={{ height: '1.25rem', width: '1.25rem', marginRight: '0.5rem' }} />
            <span style={{ fontWeight: '500' }}>Connected</span>
          </div>

          {navigator.calendly_profile && (
            <div style={{
              backgroundColor: '#f9fafb',
              borderRadius: '0.5rem',
              padding: '1rem'
            }}>
              <div className="flex items-center" style={{ marginBottom: '0.75rem' }}>
                {navigator.calendly_profile.avatar_url && (
                  <img
                    src={navigator.calendly_profile.avatar_url}
                    alt={navigator.calendly_profile.name}
                    style={{
                      height: '2.5rem',
                      width: '2.5rem',
                      borderRadius: '50%',
                      marginRight: '0.75rem'
                    }}
                  />
                )}
                <div>
                  <p style={{ fontWeight: '500', color: '#111827' }}>{navigator.calendly_profile.name}</p>
                  <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>{navigator.calendly_profile.email}</p>
                </div>
              </div>
              <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                <p><strong>Timezone:</strong> {navigator.calendly_profile.timezone}</p>
                <p><strong>Scheduling URL:</strong>
                  <a
                    href={navigator.calendly_profile.scheduling_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      color: '#3b82f6',
                      textDecoration: 'none',
                      marginLeft: '0.25rem'
                    }}
                  >
                    {navigator.calendly_profile.scheduling_url}
                  </a>
                </p>
              </div>
            </div>
          )}

          {/* Disconnect Section */}
          {!showDisconnectConfirm ? (
            <button
              onClick={() => setShowDisconnectConfirm(true)}
              style={{
                color: '#dc2626',
                fontSize: '0.875rem',
                fontWeight: '500',
                backgroundColor: 'transparent',
                border: 'none',
                cursor: 'pointer'
              }}
            >
              Disconnect Calendly
            </button>
          ) : (
            <div style={{
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '0.5rem',
              padding: '1rem'
            }}>
              <div className="flex items-center" style={{ marginBottom: '0.75rem' }}>
                <ExclamationTriangleIcon style={{ height: '1.25rem', width: '1.25rem', color: '#dc2626', marginRight: '0.5rem' }} />
                <span style={{ fontWeight: '500', color: '#991b1b' }}>Confirm Disconnection</span>
              </div>
              <p style={{ fontSize: '0.875rem', color: '#b91c1c', marginBottom: '1rem' }}>
                Are you sure you want to disconnect your Calendly account? Members will no longer be able to book appointments with you.
              </p>
              <div className="flex" style={{ gap: '0.75rem' }}>
                <button
                  onClick={handleDisconnect}
                  disabled={isDisconnecting}
                  style={{
                    backgroundColor: '#dc2626',
                    color: 'white',
                    padding: '0.5rem 1rem',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    border: 'none',
                    cursor: 'pointer',
                    opacity: isDisconnecting ? 0.5 : 1
                  }}
                >
                  {isDisconnecting ? 'Disconnecting...' : 'Yes, Disconnect'}
                </button>
                <button
                  onClick={() => setShowDisconnectConfirm(false)}
                  style={{
                    backgroundColor: 'white',
                    color: '#374151',
                    padding: '0.5rem 1rem',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    border: '1px solid #d1d5db',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          {/* Disconnected State */}
          <div className="flex items-center" style={{ color: '#6b7280' }}>
            <XCircleIcon style={{ height: '1.25rem', width: '1.25rem', marginRight: '0.5rem' }} />
            <span style={{ fontWeight: '500' }}>Not Connected</span>
          </div>

          <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>
            Connect your Calendly account to allow members to book appointments with you directly through our platform.
          </p>

          <div style={{
            backgroundColor: '#eff6ff',
            border: '1px solid #bfdbfe',
            borderRadius: '0.5rem',
            padding: '1rem'
          }}>
            <h4 style={{ fontWeight: '500', color: '#1e3a8a', marginBottom: '0.5rem' }}>What happens when you connect?</h4>
            <ul style={{ fontSize: '0.875rem', color: '#1e40af', display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
              <li>• Members can book appointments without leaving our platform</li>
              <li>• Your existing Calendly settings and availability remain unchanged</li>
              <li>• You'll receive notifications for all bookings as usual</li>
              <li>• You can disconnect at any time</li>
            </ul>
          </div>

          <button
            onClick={handleConnect}
            disabled={isConnecting}
            className="btn btn-primary"
            style={{
              width: '100%',
              backgroundColor: '#3b82f6',
              color: 'white',
              padding: '0.75rem 1rem',
              borderRadius: '0.375rem',
              fontWeight: '500',
              border: 'none',
              cursor: 'pointer',
              opacity: isConnecting ? 0.5 : 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {isConnecting ? (
              <>
                <ArrowPathIcon style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                Connecting to Calendly...
              </>
            ) : (
              <>
                <CalendarDaysIcon style={{ height: '1rem', width: '1rem', marginRight: '0.5rem' }} />
                Connect Calendly Account
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default CalendlyConnectionCard;
